"use client"

import { useEffect, useState } from "react"
import { <PERSON>ge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { api } from "@/utils/api"
import { AdminReservationData, AdminReservationsApiResponse } from "@/types/accommodation"

// Hotel data type from API
type Hotel = {
  hotel_id: number
  name: string
  chinese_name: string
  location: string
  contact_name: string
  contact_phone: string
  image_url: string
  lon: number
  lat: number
  default_checkin_date: string
  default_checkout_date: string
}

// Room type data from API
type RoomType = {
  room_id: number
  HotelId: number
  type: string
  price: number
  total: number
  obligate: number
  checkin_date: string
  checkout_date: string
  hotel_id: number
  name: string
  chinese_name: string
  location: string
  contact_name: string
  contact_phone: string
  image_url: string
  lon: number
  lat: number
  default_checkin_date: string
  default_checkout_date: string
  Id: number
  room_type: string
}

// API response type
type ApiResponse<T> = {
  code: number
  msg: string
  data: T
}

export default function AdminHotelsPage() {
  const [hotels, setHotels] = useState<Hotel[]>([])
  const [roomTypes, setRoomTypes] = useState<RoomType[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(null)
  const [isAddingHotel, setIsAddingHotel] = useState(false)
  const [isAddingRoom, setIsAddingRoom] = useState(false)

  // Fetch hotels
  const fetchHotels = async () => {
    try {
      const result = await api.get<
        | {
            code?: number
            data?: Hotel[]
            msg?: string
          }
        | Hotel[]
      >("/api/admin/accommodation/hotels")

      let hotelsData: Hotel[] = []

      // 检查是否有标准API响应格式
      if (result && typeof result === "object" && "code" in result) {
        if (result.code === 200 && result.data) {
          hotelsData = result.data
        }
      } else if (Array.isArray(result)) {
        // 处理直接返回数组的情况（向后兼容）
        hotelsData = result
      }

      setHotels(hotelsData)
    } catch (error) {
      console.error("Failed to fetch hotels:", error)
    }
  }

  // Fetch room types
  const fetchRoomTypes = async () => {
    try {
      const result = await api.get<
        | {
            code?: number
            data?: RoomType[]
            msg?: string
          }
        | RoomType[]
      >("/api/admin/accommodation/rooms")

      let roomTypesData: RoomType[] = []

      // 检查是否有标准API响应格式
      if (result && typeof result === "object" && "code" in result) {
        if (result.code === 200 && result.data) {
          roomTypesData = result.data
        }
      } else if (Array.isArray(result)) {
        // 处理直接返回数组的情况（向后兼容）
        roomTypesData = result
      }

      setRoomTypes(roomTypesData)
    } catch (error) {
      console.error("Failed to fetch room types:", error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchHotels()
    fetchRoomTypes()
  }, [])

  // 新酒店表单数据
  const [newHotel, setNewHotel] = useState<Partial<Hotel>>({
    name: "",
    chinese_name: "",
    location: "",
    contact_phone: "",
  })

  // 新房间类型表单数据
  const [newRoomType, setNewRoomType] = useState<Partial<RoomType>>({
    HotelId: 0,
    type: "",
    price: 0,
    total: 0,
  })

  // 添加酒店
  const handleAddHotel = () => {
    if (newHotel.name && newHotel.location) {
      // This would typically make an API call to add the hotel
      console.log("Adding hotel:", newHotel)
      setNewHotel({
        name: "",
        chinese_name: "",
        location: "",
        contact_phone: "",
      })
      setIsAddingHotel(false)
    }
  }

  // 添加房间类型
  const handleAddRoomType = () => {
    if (newRoomType.HotelId && newRoomType.type && newRoomType.price) {
      // This would typically make an API call to add the room type
      console.log("Adding room type:", newRoomType)
      setNewRoomType({
        HotelId: 0,
        type: "",
        price: 0,
        total: 0,
      })
      setIsAddingRoom(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* 页面标题 */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Hotel Management</h1>
          <p className="text-gray-600">Manage hotels and room configurations for IFMB 2025</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsAddingHotel(true)}>
            <i className="fas fa-plus mr-2"></i>
            Add Hotel
          </Button>
          <Button variant="outline" onClick={() => setIsAddingRoom(true)}>
            <i className="fas fa-bed mr-2"></i>
            Add Room Type
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 gap-6 md:grid-cols-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Hotels</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{hotels.length}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <i className="fas fa-hotel text-xl text-blue-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Room Types</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">{roomTypes.length}</h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-green-100">
                <i className="fas fa-bed text-xl text-green-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Total Rooms</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">
                  {roomTypes.reduce((sum, rt) => sum + rt.total, 0)}
                </h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-purple-100">
                <i className="fas fa-door-open text-xl text-purple-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-500">Available Rooms</p>
                <h3 className="mt-1 text-2xl font-bold text-gray-900">
                  {roomTypes.reduce((sum, rt) => sum + (rt.total - rt.obligate), 0)}
                </h3>
              </div>
              <div className="flex h-12 w-12 items-center justify-center rounded-full bg-orange-100">
                <i className="fas fa-key text-xl text-orange-600"></i>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* 主要内容标签页 */}
      <Tabs defaultValue="hotels" className="w-full">
        <TabsList className="mb-4">
          <TabsTrigger value="hotels">Hotels</TabsTrigger>
          <TabsTrigger value="rooms">Room Types</TabsTrigger>
          <TabsTrigger value="bookings">Bookings</TabsTrigger>
          <TabsTrigger value="merging">Room Merging</TabsTrigger>
        </TabsList>

        {/* 酒店管理 */}
        <TabsContent value="hotels" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Hotel List</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-500">Loading hotels...</div>
                </div>
              ) : (
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  {hotels.map((hotel) => (
                    <div key={hotel.hotel_id} className="rounded-lg border border-gray-200 p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="mb-2 flex items-center gap-2">
                            <h3 className="font-semibold text-gray-900">{hotel.name}</h3>
                            <Badge className="bg-green-100 text-green-800">Active</Badge>
                          </div>
                          <p className="mb-1 text-sm text-gray-600">{hotel.chinese_name}</p>
                          <p className="mb-2 text-sm text-gray-600">{hotel.location}</p>
                          <div className="mb-2 text-sm text-gray-600">
                            <span className="font-medium">Contact:</span> {hotel.contact_phone}
                          </div>
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">Check-in:</span>{" "}
                            {new Date(hotel.default_checkin_date).toLocaleDateString()} -{" "}
                            <span className="font-medium">Check-out:</span>{" "}
                            {new Date(hotel.default_checkout_date).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="ml-4 flex gap-2">
                          <Button size="sm" variant="outline">
                            <i className="fas fa-edit mr-1"></i>
                            Edit
                          </Button>
                          <Button size="sm" variant="outline">
                            <i className="fas fa-eye mr-1"></i>
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  {hotels.length === 0 && !loading && (
                    <div className="py-8 text-center text-gray-500">No hotels found.</div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 房间类型管理 */}
        <TabsContent value="rooms" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Room Types</CardTitle>
            </CardHeader>
            <CardContent>
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-gray-500">Loading room types...</div>
                </div>
              ) : (
                <div className="space-y-4">
                  {roomTypes.map((roomType) => (
                    <div key={roomType.room_id} className="rounded-lg border border-gray-200 p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="mb-2 flex items-center gap-2">
                            <h3 className="font-semibold text-gray-900">{roomType.room_type || roomType.type}</h3>
                            <Badge variant="outline">{roomType.name}</Badge>
                          </div>
                          <p className="mb-2 text-sm text-gray-600">{roomType.chinese_name}</p>
                          <div className="grid grid-cols-2 gap-4 text-sm text-gray-600 md:grid-cols-4">
                            <div>
                              <span className="font-medium">Price:</span> ¥{roomType.price}/night
                            </div>
                            <div>
                              <span className="font-medium">Total Rooms:</span> {roomType.total}
                            </div>
                            <div>
                              <span className="font-medium">Obligated:</span> {roomType.obligate}
                            </div>
                            <div>
                              <span className="font-medium">Available:</span> {roomType.total - roomType.obligate}
                            </div>
                          </div>
                          <div className="mt-2 text-sm text-gray-600">
                            <span className="font-medium">Check-in:</span>{" "}
                            {new Date(roomType.checkin_date).toLocaleDateString()} -{" "}
                            <span className="font-medium">Check-out:</span>{" "}
                            {new Date(roomType.checkout_date).toLocaleDateString()}
                          </div>
                        </div>
                        <div className="ml-4 flex gap-2">
                          <Button size="sm" variant="outline">
                            <i className="fas fa-edit mr-1"></i>
                            Edit
                          </Button>
                          <Button size="sm" variant="outline">
                            <i className="fas fa-chart-bar mr-1"></i>
                            Stats
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                  {roomTypes.length === 0 && !loading && (
                    <div className="py-8 text-center text-gray-500">No room types found.</div>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* 预订管理 */}
        <TabsContent value="bookings" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Room Bookings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="py-8 text-center">
                <i className="fas fa-calendar-check mb-4 text-4xl text-gray-400"></i>
                <h3 className="mb-2 text-lg font-medium text-gray-900">Booking Management</h3>
                <p className="mb-4 text-gray-600">View and manage all room bookings for the conference</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* 房间合并 */}
        <TabsContent value="merging" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Room Merging</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="py-8 text-center">
                <i className="fas fa-object-group mb-4 text-4xl text-gray-400"></i>
                <h3 className="mb-2 text-lg font-medium text-gray-900">Room Merging Management</h3>
                <p className="mb-4 text-gray-600">Manage room sharing and merging requests</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* 添加酒店对话框 */}
      {isAddingHotel && (
        <div className="bg-opacity-50 fixed inset-0 z-50 flex items-center justify-center bg-black">
          <div className="w-full max-w-md rounded-lg bg-white p-6">
            <h3 className="mb-4 text-lg font-semibold">Add New Hotel</h3>
            <div className="space-y-4">
              <div>
                <Label htmlFor="hotel-name">Hotel Name</Label>
                <Input
                  id="hotel-name"
                  value={newHotel.name}
                  onChange={(e) => setNewHotel({ ...newHotel, name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="hotel-chinese-name">Chinese Name</Label>
                <Input
                  id="hotel-chinese-name"
                  value={newHotel.chinese_name}
                  onChange={(e) => setNewHotel({ ...newHotel, chinese_name: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="hotel-location">Location</Label>
                <Textarea
                  id="hotel-location"
                  value={newHotel.location}
                  onChange={(e) => setNewHotel({ ...newHotel, location: e.target.value })}
                />
              </div>
              <div>
                <Label htmlFor="hotel-phone">Contact Phone</Label>
                <Input
                  id="hotel-phone"
                  value={newHotel.contact_phone}
                  onChange={(e) => setNewHotel({ ...newHotel, contact_phone: e.target.value })}
                />
              </div>
            </div>
            <div className="mt-6 flex gap-2">
              <Button onClick={handleAddHotel}>Add Hotel</Button>
              <Button variant="outline" onClick={() => setIsAddingHotel(false)}>
                Cancel
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
